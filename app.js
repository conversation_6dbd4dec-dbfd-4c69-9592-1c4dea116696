const express =  require("express")
const app = express()
const userModel = require("./models/user")
const postModel = require("./models/post")
const cookieParser = require("cookie-parser")
const bcrypt = require("bcrypt")
const jwt = require("jsonwebtoken")
const user = require("./models/user")

app.set("view engine", "ejs")
app.use(express.json())
app.use(express.urlencoded({extended: true}))
app.use(cookieParser())



app.get("/", (req, res) => {

      res.render("index");
})

app.post("/register", async (req, res) => {
    const { username, name, age, email, password } = req.body;

    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
        return res.render("index", { message: "User already exists" });
    }

    bcrypt.genSalt(10, (err, salt) => {
        if (err) return res.render("index", { message: "Something went wrong" });

        bcrypt.hash(password, salt, async (err, hash) => {
            if (err) return res.render("index", { message: "Something went wrong" });

            const newUser = await userModel.create({
                username,
                name,
                age,
                email,
                password: hash
            });

            const token = jwt.sign({ email: newUser.email, userid: newUser._id }, "shhh");
            res.cookie("token", token);
            res.send("Registration Succesfull");
        });
    });
});


app.get("/login", (req, res) => {

      res.render("login");
})

app.get("/profile", isLoggedIn, async (req, res) => {

    let user = await userModel.findOne({email: req.user.email});
    console.log(user);
    res.render("profile" , {user});
    });



app.post("/login", async (req, res) => {

    let{email,password} = req.body

    let user = await userModel.findOne({email})
    if(!user)   res.send("User not found")                                                  //return res.render("login", {message: "User not found"})
    bcrypt.compare(password, user.password, (err, result) => {
        if (err) return res.render("login", { message: "Something went wrong" });

        if (!result) return res.render("login", { message: "Incorrect password" });

       
        const token = jwt.sign({ email: user.email, userid: user._id }, "shhh");
        res.cookie("token", token);
        res.redirect("/profile");
    });  


})

app.post("/createpost", isLoggedIn, async (req, res) => {
    try {
        const { content } = req.body;

        // Find the user
        const user = await userModel.findOne({ email: req.user.email });

        // Create new post
        const newPost = await postModel.create({
            user: user._id,
            content: content
        });

        // Add post to user's posts array
        user.posts.push(newPost._id);
        await user.save();

        // Redirect back to profile with success
        res.redirect("/profile");

    } catch (error) {
        console.error("Error creating post:", error);
        res.redirect("/profile");
    }
});

app.get("/logout", (req, res) => {

      res.cookie("token","")
      res.redirect("login");
})

function isLoggedIn(req, res, next) {
    const token = req.cookies.token;
    if (!token) return res.redirect("/login");
    jwt.verify(token, "shhh", (err, decoded) => {
        if (err) return res.send("You must be logged in" );
        req.user = decoded;
        next();
    });
}




 
app.listen(3000)
