<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div class="w-full min-h-screen bg-zinc-900 text-white p-10">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-center mb-8 text-blue-400">Profile Page</h1>

            <div class="bg-zinc-800 rounded-lg p-8 shadow-lg">
                <div class="text-center mb-6">
                    <h2 class="text-3xl font-semibold text-green-400 mb-2">Hello, <%= user.name %>!</h2>
                    <p class="text-gray-300 text-lg">Welcome to your profile page</p>
                </div>

                <div class="space-y-4">
                    <div class="border-b border-zinc-600 pb-4">
                        <h3 class="text-xl font-medium text-blue-300 mb-2">User Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="text-gray-400">Name:</span>
                                <span class="ml-2 text-white font-medium"><%= user.name %></span>
                            </div>
                            <div>
                                <span class="text-gray-400">Username:</span>
                                <span class="ml-2 text-white font-medium"><%= user.username %></span>
                            </div>
                            <div>
                                <span class="text-gray-400">Email:</span>
                                <span class="ml-2 text-white font-medium"><%= user.email %></span>
                            </div>
                            <div>
                                <span class="text-gray-400">Age:</span>
                                <span class="ml-2 text-white font-medium"><%= user.age %></span>
                            </div>
                        </div>
                    </div>

                    <!-- Create New Post Form -->
                    <div class="border-b border-zinc-600 pb-6">
                        <h3 class="text-xl font-medium text-blue-300 mb-4">Create New Post</h3>
                        <form action="/createpost" method="post" class="space-y-4">
                            <div>
                                <label for="content" class="block text-gray-300 text-sm font-medium mb-2">What's on your mind?</label>
                                <textarea
                                    name="content"
                                    id="content"
                                    rows="4"
                                    class="w-full bg-zinc-700 text-white rounded-lg p-3 border border-zinc-600 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                    placeholder="Share your thoughts..."
                                    required
                                ></textarea>
                            </div>
                            <div class="flex justify-end">
                                <button
                                    type="submit"
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    Create Post
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="text-center pt-4">
                        <a href="/logout" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300">
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>